<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入大屏背景名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basic:backGroundImg:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['basic:backGroundImg:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['basic:backGroundImg:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['basic:backGroundImg:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="backGroundImgList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="背景图片" align="center">
        <template #default="scope">
          <el-image :src="scope.row.backGroundUrl" :preview-src-list="[scope.row.backGroundUrl]" preview-teleported style="width: 50px; height: 50px" fit="cover" lazy />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basic:backGroundImg:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basic:backGroundImg:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改大屏背景图片对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="backGroundImgRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="大名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入大屏背景名称" />
        </el-form-item>
        <el-form-item label="背景图片" prop="backGroundUrl">
          <ImageUpload ref="imgUpload" @update:modelValue="handleUploadSuccess" :fileSize="10" :limit="1"></ImageUpload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BackGroundImg">
import { listBackGroundImg, getBackGroundImg, delBackGroundImg, addBackGroundImg, updateBackGroundImg } from "@/api/basic/backGroundImg"

const { proxy } = getCurrentInstance()

const backGroundImgList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const uploadUrl = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    backGroundUrl: null,
    isDelete: null,
  },
  rules: {
    name: [
      { required: true, message: "请输入名称", trigger: "blur" },
    ],
    backGroundUrl: [
      { required: true, message: "请上传背景图", trigger: "blur" },
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询大屏背景图片列表 */
function getList() {
  loading.value = true
  listBackGroundImg(queryParams.value).then(response => {
    backGroundImgList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    backGroundUrl: null,
    createBy: null,
    createTime: null,
    isDelete: null,
    updateBy: null,
    updateTime: null,
    remark: null
  }
  proxy.resetForm("backGroundImgRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加大屏背景图片"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getBackGroundImg(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改大屏背景图片"
  })
}

/** 提交按钮 */
function submitForm() {
  console.log(form.value);

  proxy.$refs["backGroundImgRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateBackGroundImg(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addBackGroundImg(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除大屏背景图片？').then(function () {
    return delBackGroundImg(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('basic/backGroundImg/export', {
    ...queryParams.value
  }, `backGroundImg_${new Date().getTime()}.xlsx`)
}
function handleUploadSuccess(res) {
  debugger
  form.value.backGroundUrl = res
}
getList()
</script>
