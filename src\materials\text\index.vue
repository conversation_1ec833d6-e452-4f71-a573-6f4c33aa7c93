<!-- 文本组件 -->
<template>
  <div 
    class="text-component"
    :style="computedStyle"
  >
    {{ text }}
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

/**
 * 文本组件属性接口
 * 必须与 schema.json 中的定义保持一致
 */
interface TextProps {
  text?: string
  fontSize?: number
  color?: string
  fontWeight?: 'normal' | 'bold'
}

// 组件属性
const props = withDefaults(defineProps<TextProps>(), {
  text: '文本内容',
  fontSize: 16,
  color: '#333333',
  fontWeight: 'normal'
})

// 计算最终样式
const computedStyle = computed(() => ({
  fontSize: props.fontSize + 'px',
  color: props.color,
  fontWeight: props.fontWeight,
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-start',
  padding: '8px',
  boxSizing: 'border-box' as const,
  wordBreak: 'break-all' as const,
  lineHeight: '1.4',
  overflow: 'hidden'
}))
</script>

<style scoped>
.text-component {
  user-select: none;
  cursor: default;
}
</style>
