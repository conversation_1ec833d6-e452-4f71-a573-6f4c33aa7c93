<!-- 文本组件 -->
<template>
  <div 
    class="text-component"
    :style="textStyle"
  >
    {{ text }}
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 组件属性
const props = withDefaults(defineProps<{
  text?: string
  fontSize?: number
  color?: string
  fontWeight?: 'normal' | 'bold'
  textAlign?: 'left' | 'center' | 'right'
}>(), {
  text: '文本内容',
  fontSize: 16,
  color: '#333333',
  fontWeight: 'normal',
  textAlign: 'left'
})

// 计算文本样式
const textStyle = computed(() => ({
  fontSize: props.fontSize + 'px',
  color: props.color,
  fontWeight: props.fontWeight,
  textAlign: props.textAlign,
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  wordBreak: 'break-all',
  lineHeight: '1.4'
}))
</script>

<style scoped>
.text-component {
  user-select: none;
  cursor: default;
}
</style>
