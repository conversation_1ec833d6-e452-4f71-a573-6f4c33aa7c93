<!-- 文本组件 -->
<template>
  <div class="text-component" :style="textStyle">
    {{ text }}
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

/**
 * 文本组件属性接口
 * 必须与 MaterialDefinition 中的 defaultProps 和 schema 保持一致
 */
interface TextProps {
  text?: string;
  fontSize?: number;
  color?: string;
  fontWeight?: "normal" | "bold";
}

// 组件属性 - 使用与注册信息一致的默认值
const props = withDefaults(defineProps<TextProps>(), {
  text: "文本内容", // 与 MaterialDefinition.defaultProps.text 一致
  fontSize: 16, // 与 MaterialDefinition.defaultProps.fontSize 一致
  color: "#333333", // 与 MaterialDefinition.defaultProps.color 一致
  fontWeight: "normal", // 与 MaterialDefinition.defaultProps.fontWeight 一致
});

// 计算文本样式
const textStyle = computed(() => ({
  fontSize: props.fontSize + "px",
  color: props.color,
  fontWeight: props.fontWeight,
  width: "100%",
  height: "100%",
  display: "flex",
  alignItems: "center",
  padding: "8px",
  boxSizing: "border-box" as const,
  wordBreak: "break-all" as const,
  lineHeight: "1.4",
}));
</script>

<style scoped>
.text-component {
  user-select: none;
  cursor: default;
  overflow: hidden;
}
</style>
