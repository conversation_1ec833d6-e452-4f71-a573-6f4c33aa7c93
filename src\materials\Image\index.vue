<!-- 图片组件 -->
<template>
  <div class="image-component">
    <img 
      :src="src" 
      :alt="alt"
      :style="imageStyle"
      @error="handleError"
      @load="handleLoad"
    />
    <div v-if="error" class="error-placeholder">
      <el-icon><Picture /></el-icon>
      <span>图片加载失败</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Picture } from '@element-plus/icons-vue'

// 组件属性
const props = withDefaults(defineProps<{
  src?: string
  alt?: string
  fit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'
}>(), {
  src: '/placeholder.jpg',
  alt: '图片',
  fit: 'cover'
})

// 状态
const error = ref(false)
const loaded = ref(false)

// 计算图片样式
const imageStyle = computed(() => ({
  width: '100%',
  height: '100%',
  objectFit: props.fit,
  display: error.value ? 'none' : 'block'
}))

// 错误处理
function handleError() {
  error.value = true
  loaded.value = false
}

// 加载成功
function handleLoad() {
  error.value = false
  loaded.value = true
}
</script>

<style scoped>
.image-component {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.error-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #999;
  font-size: 12px;
}

.error-placeholder .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style>
