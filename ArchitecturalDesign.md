

> 基于 **RuoYi-Vue3**（Spring Boot 3 + Vue3 + Vite + Element-Plus）  


---

## 1 技术栈速查表
| 层级 | 技术 | 版本/备注 |
|---|---|---|
| 前端 | Vue3 `<script setup>` + TS | 3.5.x |
| 构建 | Vite | 6.x |
| UI 框架 | Element-Plus（仅右侧属性面板） | 2.9.x |
| 拖拽/缩放 | vue3-draggable-resizable | 最新 |
| 图表 | ECharts 5 | 按需引入 |
| JSON 表单 | @lljj/vue3-form-naive + naive-ui | 自动生成属性面板 |
| 状态 | Pinia | 3.x |
| 后端 | RuoYi-Vue-Boot-3.x | Spring Boot 3 |

---

## 2 目录约定（绝对路径）
ruoyi-ui/src/
├── views/
│   ├── designer/          # 设计器主页面
│   └── viewer/            # 只读运行态
├── materials/
│   └── <组件名>/
│       ├── index.vue      # 运行时组件
│       ├── schema.json    # JSON Schema（数据 + 样式）
│       └── preview.png    # 200×150 缩略图
├── components/
│   ├── Canvas.vue         # 画布容器（3×4 网格背景）
│   ├── NodeRenderer.ts    # 把 type 字符串渲染成真实组件
│   └── PropertyPanel.vue  # 右侧属性面板（自动生成）
└── stores/
└── screen.ts          # Pinia：ScreenNode[] + GlobalConfig

---

## 3 关键数据结构（永远不变）
```ts
// 画布节点
interface ScreenNode {
  id: string                 // uuid
  type: string               // 对应 materials/*/schema.json
  left: number               // px
  top: number
  width: number
  height: number
  rotate: number
  zIndex: number
  style: Record<string, any> // 边框/背景/字体
  dataSource: {
    api: string              // REST 地址
    method: 'GET' | 'POST'
    body?: any
    refresh: number          // 秒，0=不轮询
  }
  props: Record<string, any> // 由 schema.json 驱动
}

// 全局配置
interface GlobalConfig {
  bg: string                 // 背景色或图片
  size: { w: 1920; h: 1080 } // 画布分辨率
}


```
## 4 组件开发模板（复制即用）
在 materials/<组件名>/ 新建文件夹，三件套：
### 4.1 index.vue
```vue
<template>
  <v-chart :option="option" autoresize />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { BarChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
use([BarChart, CanvasRenderer])

const props = defineProps<{ node: ScreenNode }>()
const option = computed(() => ({
  title: { text: props.node.props?.title || '柱状图' },
  xAxis: { data: props.node.props?.xAxis || [] },
  yAxis: {},
  series: [{ type: 'bar', data: props.node.props?.series?.[0]?.data || [] }]
}))
</script>
```
### 4.2 schema.json
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "title":  { "type": "string", "default": "柱状图" },
    "xAxis":  { "type": "array", "items": { "type": "string" } },
    "series": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": { "data": { "type": "array", "items": { "type": "number" } } },
        "required": ["data"]
      }
    }
  },
  "required": ["xAxis", "series"]
}
```
### 4.3 preview.png
任意 200×150 PNG 缩略图

## 5 画布网格 & 缩放
设计稿：1920×1080
背景 3×4 网格线（480×270 一格）
拖拽/缩放时可选吸附到 480/270 的整数倍
## 6 运行时渲染（NodeRenderer.ts）
```ts
import { h } from 'vue'
import LineChart from '@/materials/line-chart/index.vue'
import BarChart  from '@/materials/bar-chart/index.vue'

const map: Record<string, any> = {
  'line-chart': LineChart,
  'bar-chart':  BarChart
}

export default (props: { node: ScreenNode }) => {
  const Comp = map[props.node.type]
  return Comp ? h(Comp, { node: props.node }) : null
}
```
## 7 禁止事项
不升级 Element-Plus 到 3.x
不引入 Vuex / Redux
不修改 RuoYi 原有路由和权限逻辑