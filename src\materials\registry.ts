/**
 * 物料组件注册中心
 * 管理所有可用的组件类型
 */

import type { MaterialDefinition } from "../types/component";

/**
 * 组件注册中心类
 */
class MaterialRegistry {
  private materials = new Map<string, MaterialDefinition>();

  /**
   * 注册组件
   */
  register(material: MaterialDefinition) {
    this.materials.set(material.type, material);
    console.log(`✅ 注册组件: ${material.name} (${material.type})`);
  }

  /**
   * 获取单个组件定义
   */
  get(type: string): MaterialDefinition | undefined {
    return this.materials.get(type);
  }

  /**
   * 获取所有组件定义
   */
  getAll(): MaterialDefinition[] {
    return Array.from(this.materials.values());
  }

  /**
   * 检查组件是否存在
   */
  has(type: string): boolean {
    return this.materials.has(type);
  }

  /**
   * 获取组件列表（用于物料面板显示）
   */
  getMaterialList() {
    return this.getAll().map((material) => ({
      type: material.type,
      name: material.name,
      icon: material.icon,
    }));
  }
}

// 创建全局注册中心实例
export const materialRegistry = new MaterialRegistry();

// 自动注册所有组件
export function registerAllMaterials() {
  // 这里会注册所有组件
  console.log("🚀 开始注册所有物料组件...");

  // 注册文本组件
  materialRegistry.register({
    type: "Text",
    name: "文本",
    category: "basic", // 添加分类
    icon: "Document",
    component: () => import("./text/index.vue"),
    defaultProps: {
      text: "文本内容",
      fontSize: 16,
      color: "#333333",
      fontWeight: "normal",
    },
    defaultStyle: {}, // 添加默认样式
    defaultSize: { width: 300, height: 100 },
    schema: {
      type: "object", // 添加 schema 类型
      properties: {
        text: { type: "string", title: "文本内容" },
        fontSize: {
          type: "number",
          title: "字体大小",
          minimum: 12,
          maximum: 72,
        },
        color: { type: "string", title: "文字颜色", format: "color" },
        fontWeight: {
          type: "string",
          title: "字体粗细",
          enum: ["normal", "bold"],
        },
      },
    },
  });

  console.log(
    `✅ 组件注册完成，共注册 ${materialRegistry.getAll().length} 个组件`
  );
}
