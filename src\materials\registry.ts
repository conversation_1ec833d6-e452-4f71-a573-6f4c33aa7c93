/**
 * 物料组件注册中心
 * 管理所有可用的组件类型
 */

import type { MaterialDefinition } from "../types/component";

/**
 * 组件注册中心类
 */
class MaterialRegistry {
  private materials = new Map<string, MaterialDefinition>();

  /**
   * 注册组件
   */
  register(material: MaterialDefinition) {
    this.materials.set(material.type, material);
    console.log(`✅ 注册组件: ${material.name} (${material.type})`);
  }

  /**
   * 获取单个组件定义
   */
  get(type: string): MaterialDefinition | undefined {
    return this.materials.get(type);
  }

  /**
   * 获取所有组件定义
   */
  getAll(): MaterialDefinition[] {
    return Array.from(this.materials.values());
  }

  /**
   * 检查组件是否存在
   */
  has(type: string): boolean {
    return this.materials.has(type);
  }

  /**
   * 获取组件列表（用于物料面板显示）
   */
  getMaterialList() {
    return this.getAll().map((material) => ({
      type: material.type,
      name: material.name,
      icon: material.icon,
    }));
  }
}

// 创建全局注册中心实例
export const materialRegistry = new MaterialRegistry();

/**
 * 从 schema.json 文件加载组件配置
 */
async function loadComponentSchema(componentPath: string) {
  try {
    const schemaModule = await import(`${componentPath}/schema.json`);
    return schemaModule.default;
  } catch (error) {
    console.error(`加载组件配置失败: ${componentPath}`, error);
    return null;
  }
}

/**
 * 注册单个组件（基于新的目录结构）
 */
async function registerComponent(componentName: string) {
  const componentPath = `./${componentName}`;

  // 加载 schema.json 配置
  const config = await loadComponentSchema(componentPath);
  if (!config) return;

  // 注册组件
  materialRegistry.register({
    type: config.type,
    name: config.name,
    category: config.category,
    icon: config.icon,
    thumbnail: `${componentPath}/preview.svg`, // 缩略图路径
    component: () => import(`${componentPath}/index.vue`),
    defaultProps: config.defaultProps,
    defaultStyle: config.defaultStyle,
    defaultSize: config.defaultSize,
    schema: config.schema,
  });

  console.log(`✅ 注册组件: ${config.name} (${config.type})`);
}

// 自动注册所有组件
export async function registerAllMaterials() {
  console.log("🚀 开始注册所有物料组件...");

  // 组件列表（需要手动维护，或者可以通过 Vite 的 glob 自动发现）
  const components = [
    "text",
    // 'image',
    // 'chart'
  ];

  // 并行注册所有组件
  await Promise.all(
    components.map((componentName) => registerComponent(componentName))
  );

  console.log(
    `✅ 组件注册完成，共注册 ${materialRegistry.getAll().length} 个组件`
  );
}
