/**
 * 物料组件注册中心
 * 管理所有可用的组件类型
 */

export interface MaterialDefinition {
  type: string; // 组件类型标识
  name: string; // 组件显示名称
  icon: string; // 组件图标
  component: any; // Vue组件
  defaultProps: Record<string, any>; // 默认属性
  defaultSize: { w: number; h: number }; // 默认栅格大小
  schema?: any; // 属性配置Schema（后续用于属性面板）
}

/**
 * 组件注册中心类
 */
class MaterialRegistry {
  private materials = new Map<string, MaterialDefinition>();

  /**
   * 注册组件
   */
  register(material: MaterialDefinition) {
    this.materials.set(material.type, material);
    console.log(`✅ 注册组件: ${material.name} (${material.type})`);
  }

  /**
   * 获取单个组件定义
   */
  get(type: string): MaterialDefinition | undefined {
    return this.materials.get(type);
  }

  /**
   * 获取所有组件定义
   */
  getAll(): MaterialDefinition[] {
    return Array.from(this.materials.values());
  }

  /**
   * 检查组件是否存在
   */
  has(type: string): boolean {
    return this.materials.has(type);
  }

  /**
   * 获取组件列表（用于物料面板显示）
   */
  getMaterialList() {
    return this.getAll().map((material) => ({
      type: material.type,
      name: material.name,
      icon: material.icon,
    }));
  }
}

// 创建全局注册中心实例
export const materialRegistry = new MaterialRegistry();

// 自动注册所有组件
export function registerAllMaterials() {
  // 这里会注册所有组件
  console.log("🚀 开始注册所有物料组件...");

  // 注册文本组件
  materialRegistry.register({
    type: "Text",
    name: "文本",
    icon: "Document",
    component: () => import("./text/index.vue"),
    defaultProps: {
      text: "文本内容",
      fontSize: 16,
      color: "#333333",
      fontWeight: "normal",
    },
    defaultSize: { w: 3, h: 1 },
    schema: {
      properties: {
        text: { type: "string", title: "文本内容" },
        fontSize: {
          type: "number",
          title: "字体大小",
          minimum: 12,
          maximum: 72,
        },
        color: { type: "string", title: "文字颜色", format: "color" },
        fontWeight: {
          type: "string",
          title: "字体粗细",
          enum: ["normal", "bold"],
          enumNames: ["正常", "加粗"],
        },
      },
    },
  });

  console.log(
    `✅ 组件注册完成，共注册 ${materialRegistry.getAll().length} 个组件`
  );
}
