// 画布相关类型配置
import { ComponentInstance } from "./component";
/**
 * 画布配置
 */
export interface CanvasConfig {
  width: number; // 画布宽度（像素）
  height: number; // 画布高度（像素）
  background: string; // 背景色或背景图
  scale: number; // 缩放比例 0.1-5
  showGrid: boolean; // 是否显示网格
  gridSize: number; // 网格大小（像素）
  showRuler: boolean; // 是否显示标尺
  snapToGrid: boolean; // 是否吸附网格
}
/**
 * 画布状态
 */
export interface CanvasState {
  config: CanvasConfig;
  components: ComponentInstance[];
  selectedIds: string[]; // 选中的组件ID列表（支持多选）
  clipboard: ComponentInstance[]; // 剪贴板
  mode: "edit" | "preview"; // 编辑模式或预览模式
}
