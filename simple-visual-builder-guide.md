# 🎯 简化版可视化搭建平台开发指南

## 📋 项目需求
- 物料区拖拽组件到渲染层
- 组件可以放大缩小
- 渲染层12格栅格布局
- 画布尺寸1920×1080
- 可更换大屏背景
- 简单的属性修改

## 🚀 开发路线（3周完成）

### 第一阶段：核心渲染引擎（1周）
**目标**：JSON驱动的组件渲染系统

### 第二阶段：拖拽+栅格布局（1周）  
**目标**：拖拽功能 + 12格布局 + 组件缩放

### 第三阶段：属性控制+背景（1周）
**目标**：属性面板 + 背景更换

---

## 📦 已安装的依赖

### 核心依赖
```bash
✅ pinia                    # 状态管理
✅ element-plus             # UI组件库  
✅ @element-plus/icons-vue  # 图标库
✅ nanoid                   # ID生成器
✅ lodash-es               # 工具库
✅ vuedraggable            # 拖拽功能
✅ vue-grid-layout         # 栅格布局
✅ @form-create/element-ui # 动态表单
```

### 开发依赖
```bash
✅ @types/lodash-es        # TypeScript类型
✅ sass                    # CSS预处理器
```

---

## 🎯 第一阶段：核心渲染引擎

### 1.1 核心数据结构

```typescript
// src/types/component.ts
export interface ComponentInstance {
  id: string              // 唯一ID
  type: string            // 组件类型 'Text', 'Chart'
  gridX: number           // 栅格X位置 (0-11)
  gridY: number           // 栅格Y位置
  gridW: number           // 栅格宽度 (1-12)
  gridH: number           // 栅格高度
  props: Record<string, any>    // 组件属性
  style: Record<string, any>    // 样式
}

// 画布配置
export interface CanvasConfig {
  width: 1920
  height: 1080
  background: string      // 背景图片URL或颜色
  cols: 12               // 栅格列数
}
```

### 1.2 状态管理

```typescript
// src/stores/canvas.ts
import { defineStore } from 'pinia'
import { nanoid } from 'nanoid'

export const useCanvasStore = defineStore('canvas', {
  state: () => ({
    config: {
      width: 1920,
      height: 1080,
      background: '#000000',
      cols: 12
    },
    components: [] as ComponentInstance[],
    selectedId: null as string | null
  }),
  
  actions: {
    addComponent(component: Omit<ComponentInstance, 'id'>) {
      const newComponent = {
        ...component,
        id: nanoid()
      }
      this.components.push(newComponent)
      this.selectedId = newComponent.id
      return newComponent
    },
    
    updateComponent(id: string, updates: Partial<ComponentInstance>) {
      const index = this.components.findIndex(c => c.id === id)
      if (index > -1) {
        Object.assign(this.components[index], updates)
      }
    },
    
    removeComponent(id: string) {
      const index = this.components.findIndex(c => c.id === id)
      if (index > -1) {
        this.components.splice(index, 1)
        if (this.selectedId === id) {
          this.selectedId = null
        }
      }
    },
    
    selectComponent(id: string) {
      this.selectedId = id
    },
    
    updateBackground(background: string) {
      this.config.background = background
    }
  },
  
  getters: {
    selectedComponent: (state) => 
      state.components.find(c => c.id === state.selectedId)
  }
})
```

### 1.3 组件注册中心

```typescript
// src/materials/registry.ts
export interface MaterialDefinition {
  type: string
  name: string
  icon: string
  component: any
  defaultProps: Record<string, any>
  defaultSize: { w: number; h: number }
  schema: any
}

class MaterialRegistry {
  private materials = new Map<string, MaterialDefinition>()
  
  register(material: MaterialDefinition) {
    this.materials.set(material.type, material)
  }
  
  get(type: string) {
    return this.materials.get(type)
  }
  
  getAll() {
    return Array.from(this.materials.values())
  }
}

export const materialRegistry = new MaterialRegistry()

// 注册基础组件
materialRegistry.register({
  type: 'Text',
  name: '文本',
  icon: 'Document',
  component: () => import('./Text/index.vue'),
  defaultProps: {
    text: '文本内容',
    fontSize: 16,
    color: '#ffffff'
  },
  defaultSize: { w: 3, h: 1 },
  schema: {
    properties: {
      text: { type: 'string', title: '文本内容' },
      fontSize: { type: 'number', title: '字体大小' },
      color: { type: 'string', title: '文字颜色', format: 'color' }
    }
  }
})
```

### 1.4 核心渲染器

```vue
<!-- src/components/ComponentRenderer.vue -->
<template>
  <component
    :is="componentDefinition?.component"
    v-if="componentDefinition"
    v-bind="component.props"
    :style="componentStyle"
  />
  <div v-else class="error-component">
    组件 {{ component.type }} 未找到
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { materialRegistry } from '@/materials/registry'

const props = defineProps<{
  component: ComponentInstance
}>()

const componentDefinition = computed(() => 
  materialRegistry.get(props.component.type)
)

const componentStyle = computed(() => ({
  width: '100%',
  height: '100%',
  ...props.component.style
}))
</script>
```

### 1.5 第一阶段验收标准
- [ ] 能通过JSON数据渲染组件
- [ ] 实现文本组件
- [ ] 组件注册系统正常工作

---

## 🎨 第二阶段：拖拽+栅格布局

### 2.1 主编辑器布局

```vue
<!-- src/views/Editor.vue -->
<template>
  <div class="editor">
    <!-- 左侧物料区 -->
    <div class="materials-panel">
      <MaterialsPanel />
    </div>
    
    <!-- 中间画布区 -->
    <div class="canvas-area">
      <CanvasToolbar />
      <GridCanvas />
    </div>
    
    <!-- 右侧属性区 -->
    <div class="property-panel">
      <PropertyPanel />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.editor {
  display: flex;
  height: 100vh;
  
  .materials-panel {
    width: 280px;
    background: #fff;
    border-right: 1px solid #e4e7ed;
  }
  
  .canvas-area {
    flex: 1;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
  }
  
  .property-panel {
    width: 320px;
    background: #fff;
    border-left: 1px solid #e4e7ed;
  }
}
</style>
```

### 2.2 栅格画布

```vue
<!-- src/components/GridCanvas.vue -->
<template>
  <div class="canvas-container">
    <div 
      class="canvas"
      :style="canvasStyle"
      @drop="handleDrop"
      @dragover.prevent
    >
      <!-- 栅格布局容器 -->
      <grid-layout
        v-model:layout="layout"
        :col-num="12"
        :row-height="90"
        :is-draggable="true"
        :is-resizable="true"
        :margin="[10, 10]"
        :use-css-transforms="true"
        @layout-updated="handleLayoutUpdate"
      >
        <grid-item
          v-for="item in layout"
          :key="item.i"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
          @click="selectComponent(item.i)"
          :class="{ selected: selectedId === item.i }"
        >
          <ComponentRenderer :component="getComponent(item.i)" />
          
          <!-- 删除按钮 -->
          <div 
            v-if="selectedId === item.i"
            class="delete-btn"
            @click.stop="removeComponent(item.i)"
          >
            <el-icon><Delete /></el-icon>
          </div>
        </grid-item>
      </grid-layout>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { GridLayout, GridItem } from 'vue-grid-layout'
import { useCanvasStore } from '@/stores/canvas'
import ComponentRenderer from './ComponentRenderer.vue'

const canvasStore = useCanvasStore()

// 画布样式
const canvasStyle = computed(() => ({
  width: canvasStore.config.width + 'px',
  height: canvasStore.config.height + 'px',
  background: canvasStore.config.background.startsWith('#') 
    ? canvasStore.config.background 
    : `url(${canvasStore.config.background})`,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  position: 'relative',
  margin: '20px auto',
  boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
}))

// 栅格布局数据
const layout = computed({
  get: () => canvasStore.components.map(c => ({
    i: c.id,
    x: c.gridX,
    y: c.gridY,
    w: c.gridW,
    h: c.gridH
  })),
  set: (newLayout) => {
    newLayout.forEach(item => {
      canvasStore.updateComponent(item.i, {
        gridX: item.x,
        gridY: item.y,
        gridW: item.w,
        gridH: item.h
      })
    })
  }
})

const selectedId = computed(() => canvasStore.selectedId)

function getComponent(id: string) {
  return canvasStore.components.find(c => c.id === id)
}

function selectComponent(id: string) {
  canvasStore.selectComponent(id)
}

function removeComponent(id: string) {
  canvasStore.removeComponent(id)
}

function handleLayoutUpdate(newLayout: any[]) {
  // 布局更新处理
}

// 处理从物料区拖拽
function handleDrop(event: DragEvent) {
  const materialType = event.dataTransfer?.getData('material-type')
  if (!materialType) return
  
  // 计算放置的栅格位置
  const rect = event.currentTarget.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  // 转换为栅格坐标（简化计算）
  const gridX = Math.floor((x / rect.width) * 12)
  const gridY = Math.floor(y / 90) // 每行高度90px
  
  addComponentFromMaterial(materialType, gridX, gridY)
}

function addComponentFromMaterial(type: string, gridX: number, gridY: number) {
  const material = materialRegistry.get(type)
  if (!material) return
  
  canvasStore.addComponent({
    type,
    gridX,
    gridY,
    gridW: material.defaultSize.w,
    gridH: material.defaultSize.h,
    props: { ...material.defaultProps },
    style: {}
  })
}
</script>

<style lang="scss" scoped>
.canvas-container {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.canvas {
  position: relative;
}

.selected {
  outline: 2px solid #409eff;
}

.delete-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 20px;
  height: 20px;
  background: #f56c6c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  font-size: 12px;
}
</style>
```

### 2.3 第二阶段验收标准
- [ ] 能从物料区拖拽到画布
- [ ] 12格栅格布局正常工作
- [ ] 组件可以拖拽移动和缩放
- [ ] 组件选中状态正确显示

---

## ⚙️ 第三阶段：属性控制+背景

### 3.1 属性面板

```vue
<!-- src/components/PropertyPanel.vue -->
<template>
  <div class="property-panel">
    <div v-if="selectedComponent">
      <h3>{{ selectedComponent.type }} 属性</h3>
      
      <!-- 使用 form-create 动态生成表单 -->
      <form-create
        v-model="formData"
        :rule="formRule"
        :option="formOption"
        @change="handleFormChange"
      />
    </div>
    
    <div v-else class="no-selection">
      请选择一个组件
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { materialRegistry } from '@/materials/registry'

const canvasStore = useCanvasStore()
const formData = ref({})

const selectedComponent = computed(() => canvasStore.selectedComponent)

// 根据组件Schema生成表单规则
const formRule = computed(() => {
  if (!selectedComponent.value) return []
  
  const material = materialRegistry.get(selectedComponent.value.type)
  if (!material?.schema) return []
  
  return Object.entries(material.schema.properties).map(([key, prop]: [string, any]) => ({
    type: getControlType(prop),
    field: key,
    title: prop.title,
    value: prop.default,
    props: getControlProps(prop)
  }))
})

const formOption = {
  submitBtn: false,
  resetBtn: false,
  form: { labelWidth: '80px', size: 'small' }
}

// 监听选中组件变化
watch(selectedComponent, (component) => {
  if (component) {
    formData.value = { ...component.props }
  }
}, { immediate: true })

function handleFormChange(data: any) {
  if (selectedComponent.value) {
    canvasStore.updateComponent(selectedComponent.value.id, {
      props: { ...data }
    })
  }
}

function getControlType(prop: any) {
  if (prop.format === 'color') return 'ColorPicker'
  if (prop.type === 'number') return 'InputNumber'
  if (prop.type === 'boolean') return 'switch'
  return 'input'
}

function getControlProps(prop: any) {
  const props: any = {}
  if (prop.type === 'number') {
    props.min = prop.minimum || 0
    props.max = prop.maximum || 100
  }
  return props
}
</script>
```

### 3.2 背景设置

```vue
<!-- src/components/BackgroundSetting.vue -->
<template>
  <div class="background-setting">
    <h4>背景设置</h4>
    
    <el-radio-group v-model="backgroundType" @change="handleTypeChange">
      <el-radio label="color">纯色</el-radio>
      <el-radio label="image">图片</el-radio>
    </el-radio-group>
    
    <div class="setting-content">
      <!-- 颜色选择 -->
      <el-color-picker
        v-if="backgroundType === 'color'"
        v-model="backgroundColor"
        @change="handleColorChange"
      />
      
      <!-- 图片上传 -->
      <div v-else>
        <el-upload
          :show-file-list="false"
          :before-upload="handleImageUpload"
          accept="image/*"
        >
          <el-button type="primary">选择图片</el-button>
        </el-upload>
        
        <!-- 预设背景 -->
        <div class="preset-backgrounds">
          <div
            v-for="bg in presetBackgrounds"
            :key="bg.url"
            class="bg-item"
            @click="selectPresetBackground(bg.url)"
          >
            <img :src="bg.thumbnail" :alt="bg.name" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useCanvasStore } from '@/stores/canvas'

const canvasStore = useCanvasStore()
const backgroundType = ref('color')
const backgroundColor = ref('#000000')

// 预设背景图片
const presetBackgrounds = [
  { name: '星空', url: '/backgrounds/starry.jpg', thumbnail: '/backgrounds/starry-thumb.jpg' },
  { name: '科技', url: '/backgrounds/tech.jpg', thumbnail: '/backgrounds/tech-thumb.jpg' },
  { name: '城市', url: '/backgrounds/city.jpg', thumbnail: '/backgrounds/city-thumb.jpg' }
]

function handleTypeChange() {
  if (backgroundType.value === 'color') {
    handleColorChange(backgroundColor.value)
  }
}

function handleColorChange(color: string) {
  canvasStore.updateBackground(color)
}

function handleImageUpload(file: File) {
  // 这里可以上传到服务器，简化处理直接使用本地URL
  const url = URL.createObjectURL(file)
  canvasStore.updateBackground(url)
  return false // 阻止默认上传
}

function selectPresetBackground(url: string) {
  canvasStore.updateBackground(url)
}
</script>

<style lang="scss" scoped>
.background-setting {
  padding: 16px;
  
  .preset-backgrounds {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-top: 16px;
    
    .bg-item {
      cursor: pointer;
      border: 2px solid transparent;
      border-radius: 4px;
      overflow: hidden;
      
      &:hover {
        border-color: #409eff;
      }
      
      img {
        width: 100%;
        height: 60px;
        object-fit: cover;
      }
    }
  }
}
</style>
```

### 3.3 第三阶段验收标准
- [ ] 选中组件时显示属性面板
- [ ] 修改属性能实时更新组件
- [ ] 背景颜色/图片切换正常
- [ ] 预设背景图片可选择

---

## 🎯 项目完成标准

### 最终验收
- [ ] 从物料区拖拽组件到画布
- [ ] 组件在12格栅格中正确布局
- [ ] 组件可以拖拽移动和缩放
- [ ] 属性面板可以修改组件属性
- [ ] 背景可以切换颜色或图片
- [ ] 整体操作流畅无卡顿

### 测试场景
1. 拖拽文本组件到画布
2. 调整组件大小和位置
3. 修改文本内容和颜色
4. 切换画布背景
5. 添加多个组件测试布局

---

**这个简化版本专注于您的核心需求，去掉了复杂的功能，3周内完全可以实现！**
