{
  "compilerOptions": {
    /* ========= 基础环境 ========= */
    "target": "ES2016",                // 编译后的 JS 语法版本（ES2016 ≈ 现代浏览器 + Node 8+）
    "module": "CommonJS",              // 生成 CommonJS 模块（require/module.exports），与 Node 默认一致
    "moduleResolution": "node",        // 按 Node.js 规则找模块（node_modules、index.js 等）
    "esModuleInterop": true,           // 允许 import xx from 'cjs-pkg'，兼容旧 CommonJS 包
    "allowSyntheticDefaultImports": true, // 同上，给类型系统用的“兜底”
    "forceConsistentCasingInFileNames": true, // 强制文件名大小写一致（跨平台安全）

    /* ========= 全部严格检查关掉（最宽松） ========= */
    "strict": false,                   // 一键关闭所有严格族选项（下面 8 项随之失效）
    "noImplicitAny": false,            // 允许变量隐式 any（不写类型也不报错）
    "strictNullChecks": false,         // null / undefined 可以赋给任何类型
    "strictFunctionTypes": false,      // 函数参数逆变检查关闭
    "strictBindCallApply": false,      // 允许 call/apply/bind 任意参数
    "strictPropertyInitialization": false, // 类属性可不初始化
    "noImplicitThis": false,           // this 隐式 any 不报错
    "useUnknownInCatchVariables": false, // catch(e) 中 e 保持 any
    "noImplicitReturns": false,        // 函数缺少 return 不报错
    "noFallthroughCasesInSwitch": false, // switch 贯穿不报错
    "noUncheckedIndexedAccess": false, // obj[key] 不自动加 | undefined
    "exactOptionalPropertyTypes": false, // 可选属性仍允许 undefined
    "noImplicitOverride": false,       // 子类重写方法可不加 override
    "noPropertyAccessFromIndexSignature": false, // 允许 obj.key 访问索引签名属性
    "allowUnusedLabels": true,         // 允许未使用的 label:
    "allowUnreachableCode": true,      // 允许永远不会执行的代码

    /* ========= JS 混用支持 ========= */
    "allowJs": true,                   // 项目里可同时写 .ts 与 .js
    "checkJs": false,                  // 不对 .js 文件做类型检查

    /* ========= 编译加速 ========= */
    "skipLibCheck": true,              // 跳过 node_modules/@types 类型检查，减少时间

    /* ========= 输入/输出路径 ========= */
    "outDir": "./dist",                // 编译产物输出到 dist 目录
    "rootDir": "./src",                // 源码根目录（保持目录结构）
    "resolveJsonModule": true,         // 允许 import data from './x.json' 并带类型
    "declaration": false,              // 不生成 .d.ts 声明文件（纯前端项目可省）
    "sourceMap": true,                 // 生成 .map 方便调试

    /* ========= 其他常用 ========= */
    "removeComments": false            // 保留注释（可按需改 true 去掉）
  },

  /* ========= 包含/排除文件 ========= */
  "include": ["src/**/*"],             // 只编译 src 目录
  "exclude": ["node_modules", "dist"]  // 忽略依赖和输出目录
}