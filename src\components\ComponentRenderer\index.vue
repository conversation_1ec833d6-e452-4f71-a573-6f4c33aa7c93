<!-- 核心组件  JSON渲染器 -->
<template>
  <component
    :is="componentType"
    v-if="componentType"
    v-bind="component.props"
    :style="componentStyle"
  />
  <div v-else class="error-component">
    <el-icon><Warning /></el-icon>
    <span>组件 {{ component.type }} 未找到</span>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { materialRegistry } from "@/materials/registry";
import { Warning } from "@element-plus/icons-vue";
import { ComponentSchema } from "../../types/component";

// 接收组件数据
const props = defineProps({
  component: {
    type: Object,
    required: true,
  },
});

// 根据type获取组件
const componentType = computed(() => {
  const material = materialRegistry.get(props.component.type);
  return material?.component;
});

// 计算样式
const componentStyle = computed(() => ({
  width: "100%",
  height: "100%",
  ...props.component.style,
}));
</script>

<style lang="scss" scoped>
.error-component {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #ffebee;
  border: 1px dashed #f44336;
  color: #f44336;
  font-size: 12px;
  gap: 8px;

  .el-icon {
    font-size: 20px;
  }
}
</style>
