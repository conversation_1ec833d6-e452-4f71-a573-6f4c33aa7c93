import { defineStore } from "pinia";
import { nanoid } from "nanoid";
import type { ComponentInstance } from "../../types/component";
export const useCanvasStore = defineStore("canvas", {
  state: () => ({
    config: {
      width: 1920,
      height: 1080,
      background: "#000000",
      cols: 12,
    },
    components: [] as ComponentInstance[],
    selectedId: null as string | null,
  }),
  actions:() => {
    
  }
});
