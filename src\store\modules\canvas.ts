import { defineStore } from "pinia";
import { nanoid } from "nanoid";
import type { ComponentInstance } from "../../types/component";
export const useCanvasStore = defineStore("canvas", {
  state: () => ({
    config: {
      width: 1920,
      height: 1080,
      background: "#000000",
      cols: 12,
    },
    components: [] as ComponentInstance[],
    selectedId: null as string | null,
    mode: "edit" as "edit" | "preview", //模式
    isDragging: false, //是否拖拽
    scale: 1, //缩放比例
  }),
  actions: {
    /**新增物料 */
    addComponent(component: Omit<ComponentInstance, "id">) {
      const newComponent: ComponentInstance = {
        id: nanoid(),
        ...component,
      };
      this.components.push(newComponent);
      this.selectedId = newComponent.id;
      return newComponent;
    },
    /**更新物料 */
    updateComponent(id: string, updates: Partial<ComponentInstance>) {
      const index = this.components.findIndex((c) => c.id === id);
      if (index > -1) {
        Object.assign(this.components[index], updates);
      }
    },
    removeComponent(id: string) {
      const index = this.components.findIndex((c) => c.id === id);
      if (index > -1) {
        this.components.splice(index, 1);
        if (this.selectedId === id) {
          this.selectedId = null; // 清除选中状态
        }
      }
    },
    /**选择物料 */
    selectComponent(id: string) {
      this.selectedId = id;
    },
    /**更新背景图 */
    updateBackground(background: string) {
      this.config.background = background;
    },
  },
  getters: {
    selectedComponent: (state) =>
      state.components.find((c) => c.id === state.selectedId),
  },
});
