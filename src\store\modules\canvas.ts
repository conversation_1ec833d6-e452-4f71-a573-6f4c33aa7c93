import { defineStore } from "pinia";
import { nanoid } from "nanoid";
import type { ComponentInstance } from "../../types/component";
export const useCanvasStore = defineStore("canvas", {
  state: () => ({
    config: {
      width: 1920,
      height: 1080,
      background: "#000000",
      cols: 12,
    },
    components: [] as ComponentInstance[],
    selectedId: null as string | null,
    mode: "edit" as "edit" | "preview", //模式
    isDragging: false, //是否拖拽
    scale: 1, //缩放比例
  }),
  actions: () => {
    /**新增物料 */
    addComponent(conmpoent<ComponentInstance>){

    }
  },
});
