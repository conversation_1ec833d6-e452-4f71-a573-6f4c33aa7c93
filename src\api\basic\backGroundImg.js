import request from '@/utils/request'

// 查询大屏背景图片列表
export function listBackGroundImg(query) {
  return request({
    url: '/basic/backGroundImg/list',
    method: 'get',
    params: query
  })
}

// 查询大屏背景图片详细
export function getBackGroundImg(id) {
  return request({
    url: '/basic/backGroundImg/' + id,
    method: 'get'
  })
}

// 新增大屏背景图片
export function addBackGroundImg(data) {
  return request({
    url: '/basic/backGroundImg',
    method: 'post',
    data: data
  })
}

// 修改大屏背景图片
export function updateBackGroundImg(data) {
  return request({
    url: '/basic/backGroundImg',
    method: 'put',
    data: data
  })
}

// 删除大屏背景图片
export function delBackGroundImg(id) {
  return request({
    url: '/basic/backGroundImg/' + id,
    method: 'delete'
  })
}
