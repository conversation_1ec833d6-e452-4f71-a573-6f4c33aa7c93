# 🎯 可视化搭建平台开发完整指南

## 📋 开发路线图

### 第一阶段：核心渲染引擎（1-2周）
**目标**：实现JSON驱动的组件渲染系统  
**重要性**：★★★★★（最核心）

### 第二阶段：基础画布（1周）
**目标**：组件显示、选择、基础操作  
**重要性**：★★★★☆

### 第三阶段：拖拽系统（1周）
**目标**：物料区到画布的拖拽  
**重要性**：★★★★☆

### 第四阶段：属性配置（1-2周）
**目标**：动态属性配置面板  
**重要性**：★★★★☆

### 第五阶段：完善功能（1-2周）
**目标**：撤销重做、保存加载等  
**重要性**：★★★☆☆

---

## 🚀 第一阶段：核心渲染引擎

### 1.1 项目初始化

#### 技术栈选择
```bash
# 核心框架
Vue 3.4+           # 现代化前端框架，支持Composition API
TypeScript 5+      # 类型安全，提升开发体验
Vite 5+           # 快速构建工具，热更新

# 必需依赖
pinia             # Vue官方状态管理库，替代Vuex
element-plus      # 成熟的Vue3 UI组件库
@element-plus/icons-vue  # Element Plus图标库
nanoid            # 轻量级唯一ID生成器
lodash-es         # 现代化工具库，支持tree-shaking

# 开发依赖
@vitejs/plugin-vue    # Vite的Vue插件
vue-tsc              # Vue的TypeScript编译器
sass                 # CSS预处理器
@types/lodash-es     # Lodash的TypeScript类型定义
```

#### 安装命令
```bash
# 创建项目
npm create vue@latest visual-builder
cd visual-builder

# 安装核心依赖
npm install pinia element-plus @element-plus/icons-vue nanoid lodash-es

# 安装开发依赖
npm install -D sass @types/lodash-es

# 后续阶段需要的库（暂不安装）
# npm install vuedraggable sortablejs  # 第三阶段：拖拽
# npm install @vueuse/core             # 第二阶段：组合式工具
# npm install immer                    # 第五阶段：不可变数据
```

#### 项目结构
```
src/
├── types/              # TypeScript类型定义
│   ├── component.ts    # 组件相关类型
│   ├── canvas.ts       # 画布相关类型
│   └── material.ts     # 物料相关类型
├── stores/             # Pinia状态管理
│   ├── canvas.ts       # 画布状态
│   ├── material.ts     # 物料状态
│   └── history.ts      # 历史记录状态
├── components/         # 通用组件
│   ├── ComponentRenderer.vue  # 核心渲染器
│   ├── Canvas.vue             # 画布组件
│   └── PropertyPanel.vue     # 属性面板
├── materials/          # 物料组件库
│   ├── registry.ts     # 组件注册中心
│   ├── Text/          # 文本组件
│   ├── Image/         # 图片组件
│   └── Chart/         # 图表组件
├── views/             # 页面组件
│   └── Editor.vue     # 主编辑器页面
├── utils/             # 工具函数
│   ├── id.ts          # ID生成工具
│   ├── style.ts       # 样式计算工具
│   └── schema.ts      # Schema处理工具
└── main.ts            # 应用入口
```

### 1.2 核心数据结构设计

#### 组件数据结构
```typescript
// src/types/component.ts

/**
 * 画布上的组件实例
 */
export interface ComponentInstance {
  id: string              // 唯一标识，使用nanoid生成
  type: string            // 组件类型，如 'Text', 'BarChart', 'Image'
  name: string            // 组件名称，用于图层面板显示
  x: number              // X坐标（像素）
  y: number              // Y坐标（像素）
  width: number          // 宽度（像素）
  height: number         // 高度（像素）
  zIndex: number         // 层级，数值越大越在上层
  rotation: number       // 旋转角度（度）
  opacity: number        // 透明度 0-1
  visible: boolean       // 是否可见
  locked: boolean        // 是否锁定（锁定后不可编辑）
  props: Record<string, any>    // 组件特有属性
  style: Record<string, any>    // CSS样式
  events?: ComponentEvent[]     // 事件配置（第四阶段）
  animations?: Animation[]      // 动画配置（第五阶段）
}

/**
 * 物料组件定义
 */
export interface MaterialDefinition {
  type: string            // 组件类型，全局唯一
  name: string            // 显示名称
  category: string        // 分类：'basic', 'chart', 'media', 'layout'
  icon: string            // 图标名称（Element Plus图标）
  thumbnail?: string      // 缩略图URL
  component: any          // Vue组件（动态导入）
  schema: ComponentSchema // 属性配置Schema
  defaultProps: Record<string, any>  // 默认属性
  defaultStyle: Record<string, any>  // 默认样式
  defaultSize: {          // 默认尺寸
    width: number
    height: number
  }
}

/**
 * 组件属性Schema（JSON Schema格式）
 */
export interface ComponentSchema {
  type: 'object'
  properties: Record<string, PropertySchema>
  required?: string[]
}

export interface PropertySchema {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  title: string           // 属性显示名称
  description?: string    // 属性描述
  default?: any          // 默认值
  enum?: any[]           // 枚举值（用于下拉选择）
  minimum?: number       // 数字最小值
  maximum?: number       // 数字最大值
  step?: number          // 数字步长
  format?: 'color' | 'textarea' | 'url' | 'email' | 'date'  // 特殊格式
  items?: PropertySchema // 数组项Schema
}
```

#### 画布数据结构
```typescript
// src/types/canvas.ts

/**
 * 画布配置
 */
export interface CanvasConfig {
  width: number          // 画布宽度（像素）
  height: number         // 画布高度（像素）
  background: string     // 背景色或背景图
  scale: number          // 缩放比例 0.1-5
  showGrid: boolean      // 是否显示网格
  gridSize: number       // 网格大小（像素）
  showRuler: boolean     // 是否显示标尺
  snapToGrid: boolean    // 是否吸附网格
}

/**
 * 画布状态
 */
export interface CanvasState {
  config: CanvasConfig
  components: ComponentInstance[]
  selectedIds: string[]  // 选中的组件ID列表（支持多选）
  clipboard: ComponentInstance[]  // 剪贴板
  mode: 'edit' | 'preview'       // 编辑模式或预览模式
}
```

### 1.3 核心渲染器实现

#### 关键文件说明

**1. `src/components/ComponentRenderer.vue` - 核心渲染器**
- 负责根据JSON数据渲染Vue组件
- 处理组件的位置、大小、样式计算
- 这是整个系统的心脏

**2. `src/stores/canvas.ts` - 画布状态管理**
- 管理所有组件数据
- 提供增删改查组件的方法
- 处理选中状态、历史记录

**3. `src/materials/registry.ts` - 组件注册中心**
- 注册所有可用的物料组件
- 提供根据type获取组件定义的方法
- 管理组件分类和搜索

#### 核心渲染逻辑
```typescript
// ComponentRenderer.vue 核心逻辑（伪代码）
function renderComponent(componentData: ComponentInstance) {
  // 1. 根据type从注册中心获取组件定义
  const definition = materialRegistry.get(componentData.type)
  if (!definition) {
    return renderErrorComponent(componentData.type)
  }
  
  // 2. 计算组件的最终样式
  const finalStyle = {
    position: 'absolute',
    left: componentData.x + 'px',
    top: componentData.y + 'px',
    width: componentData.width + 'px',
    height: componentData.height + 'px',
    zIndex: componentData.zIndex,
    transform: `rotate(${componentData.rotation}deg)`,
    opacity: componentData.opacity,
    ...componentData.style
  }
  
  // 3. 使用Vue的h函数渲染组件
  return h(definition.component, {
    ...componentData.props,
    style: finalStyle,
    'data-component-id': componentData.id
  })
}
```

### 1.4 第一阶段具体实现步骤

#### 步骤1：创建基础组件（1-2天）
```typescript
// src/materials/Text/index.vue - 文本组件
<template>
  <div class="text-component" :style="textStyle">
    {{ text }}
  </div>
</template>

<script setup lang="ts">
interface Props {
  text: string
  fontSize: number
  color: string
  fontWeight: 'normal' | 'bold'
  textAlign: 'left' | 'center' | 'right'
}

const props = withDefaults(defineProps<Props>(), {
  text: '文本内容',
  fontSize: 16,
  color: '#333333',
  fontWeight: 'normal',
  textAlign: 'left'
})

const textStyle = computed(() => ({
  fontSize: props.fontSize + 'px',
  color: props.color,
  fontWeight: props.fontWeight,
  textAlign: props.textAlign,
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center'
}))
</script>
```

#### 步骤2：实现组件注册中心（1天）
```typescript
// src/materials/registry.ts
import { MaterialDefinition } from '@/types/component'

class MaterialRegistry {
  private materials = new Map<string, MaterialDefinition>()
  
  register(material: MaterialDefinition) {
    this.materials.set(material.type, material)
  }
  
  get(type: string): MaterialDefinition | undefined {
    return this.materials.get(type)
  }
  
  getByCategory(category: string): MaterialDefinition[] {
    return Array.from(this.materials.values())
      .filter(m => m.category === category)
  }
  
  getAll(): MaterialDefinition[] {
    return Array.from(this.materials.values())
  }
}

export const materialRegistry = new MaterialRegistry()

// 注册文本组件
materialRegistry.register({
  type: 'Text',
  name: '文本',
  category: 'basic',
  icon: 'Document',
  component: () => import('./Text/index.vue'),
  schema: {
    type: 'object',
    properties: {
      text: { type: 'string', title: '文本内容', default: '文本内容' },
      fontSize: { type: 'number', title: '字体大小', default: 16, minimum: 12, maximum: 72 },
      color: { type: 'string', title: '文字颜色', format: 'color', default: '#333333' }
    }
  },
  defaultProps: {
    text: '文本内容',
    fontSize: 16,
    color: '#333333'
  },
  defaultStyle: {},
  defaultSize: { width: 200, height: 50 }
})
```

#### 步骤3：实现状态管理（1-2天）
```typescript
// src/stores/canvas.ts
import { defineStore } from 'pinia'
import { ComponentInstance, CanvasState } from '@/types'
import { nanoid } from 'nanoid'

export const useCanvasStore = defineStore('canvas', {
  state: (): CanvasState => ({
    config: {
      width: 1920,
      height: 1080,
      background: '#f0f0f0',
      scale: 1,
      showGrid: true,
      gridSize: 10,
      showRuler: true,
      snapToGrid: true
    },
    components: [],
    selectedIds: [],
    clipboard: [],
    mode: 'edit'
  }),
  
  getters: {
    selectedComponents: (state) => 
      state.components.filter(c => state.selectedIds.includes(c.id)),
    
    getComponentById: (state) => (id: string) =>
      state.components.find(c => c.id === id)
  },
  
  actions: {
    addComponent(component: Omit<ComponentInstance, 'id'>) {
      const newComponent: ComponentInstance = {
        ...component,
        id: nanoid()
      }
      this.components.push(newComponent)
      this.selectedIds = [newComponent.id]
      return newComponent
    },
    
    updateComponent(id: string, updates: Partial<ComponentInstance>) {
      const index = this.components.findIndex(c => c.id === id)
      if (index > -1) {
        Object.assign(this.components[index], updates)
      }
    },
    
    removeComponent(id: string) {
      const index = this.components.findIndex(c => c.id === id)
      if (index > -1) {
        this.components.splice(index, 1)
        this.selectedIds = this.selectedIds.filter(sid => sid !== id)
      }
    },
    
    selectComponent(id: string, multiple = false) {
      if (multiple) {
        if (this.selectedIds.includes(id)) {
          this.selectedIds = this.selectedIds.filter(sid => sid !== id)
        } else {
          this.selectedIds.push(id)
        }
      } else {
        this.selectedIds = [id]
      }
    },
    
    clearSelection() {
      this.selectedIds = []
    }
  }
})
```

#### 步骤4：实现核心渲染器（2-3天）
```vue
<!-- src/components/ComponentRenderer.vue -->
<template>
  <component
    :is="componentDefinition?.component"
    v-if="componentDefinition"
    v-bind="component.props"
    :style="componentStyle"
    :data-component-id="component.id"
  />
  <div v-else class="error-component" :style="componentStyle">
    组件 {{ component.type }} 未找到
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ComponentInstance } from '@/types/component'
import { materialRegistry } from '@/materials/registry'

interface Props {
  component: ComponentInstance
}

const props = defineProps<Props>()

// 获取组件定义
const componentDefinition = computed(() => 
  materialRegistry.get(props.component.type)
)

// 计算组件样式
const componentStyle = computed(() => ({
  position: 'absolute',
  left: props.component.x + 'px',
  top: props.component.y + 'px',
  width: props.component.width + 'px',
  height: props.component.height + 'px',
  zIndex: props.component.zIndex,
  transform: `rotate(${props.component.rotation}deg)`,
  opacity: props.component.opacity,
  visibility: props.component.visible ? 'visible' : 'hidden',
  pointerEvents: props.component.locked ? 'none' : 'auto',
  ...props.component.style
}))
</script>

<style scoped>
.error-component {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffebee;
  border: 1px dashed #f44336;
  color: #f44336;
  font-size: 12px;
}
</style>
```

### 1.5 第一阶段完成标准

**功能验收标准**：
- [ ] 能够通过修改store中的components数组看到组件实时更新
- [ ] 至少实现3个基础组件（文本、矩形、图片）
- [ ] 组件能正确显示位置、大小、旋转、透明度
- [ ] 组件注册系统工作正常
- [ ] 错误处理：未知组件类型显示错误提示

**测试方法**：
```javascript
// 在浏览器控制台执行，验证渲染系统
const canvasStore = useCanvasStore()

// 添加测试组件
canvasStore.addComponent({
  type: 'Text',
  name: '测试文本',
  x: 100,
  y: 100,
  width: 200,
  height: 50,
  zIndex: 1,
  rotation: 0,
  opacity: 1,
  visible: true,
  locked: false,
  props: { text: '测试文本', fontSize: 18, color: '#ff0000' },
  style: {}
})

// 修改组件属性，观察是否实时更新
canvasStore.updateComponent(componentId, {
  props: { text: '修改后的文本', color: '#00ff00' }
})
```

---

## 🎨 第二阶段：基础画布

### 2.1 需要的技术和库

```bash
# 新增依赖
npm install @vueuse/core        # Vue组合式工具库，提供鼠标、键盘等事件处理
npm install @vueuse/gesture     # 手势库，处理拖拽、缩放等操作
```

### 2.2 画布核心功能

#### 技术要点
- **事件处理**：鼠标点击、拖拽、键盘快捷键
- **坐标计算**：考虑画布缩放、滚动偏移
- **选中状态**：边框高亮、控制点显示
- **性能优化**：大量组件时的渲染优化

#### 关键组件
1. `src/components/Canvas.vue` - 画布容器
2. `src/components/SelectionBox.vue` - 选中框
3. `src/components/ControlPoints.vue` - 控制点
4. `src/composables/useCanvasEvents.ts` - 画布事件处理

### 2.3 完成标准
- [ ] 画布能显示所有组件
- [ ] 点击组件能选中（显示边框和控制点）
- [ ] 支持多选（Ctrl+点击）
- [ ] 能拖拽移动组件位置
- [ ] 支持画布缩放（鼠标滚轮+Ctrl）
- [ ] 支持画布平移（空格+拖拽）
- [ ] 网格显示和吸附功能

---

## 🔄 第三阶段：拖拽系统

### 3.1 拖拽相关库

```bash
# 拖拽核心库
npm install vuedraggable@next   # Vue3版本的拖拽库
npm install sortablejs           # 底层拖拽引擎

# 可选：更现代的拖拽库
npm install @dnd-kit/core       # 现代拖拽库（可选）
npm install @dnd-kit/sortable   # 排序拖拽（可选）
```

### 3.2 拖拽系统架构

#### 物料区拖拽配置
```javascript
// 物料区：只能拖出，不能拖入，拖拽时克隆
{
  group: { 
    name: 'components', 
    pull: 'clone',      // 拖拽时克隆
    put: false          // 不允许拖入
  },
  sort: false,          // 不允许排序
  clone: (original) => createComponentInstance(original)
}
```

#### 画布区拖拽配置
```javascript
// 画布：可以拖入，不能拖出
{
  group: { 
    name: 'components', 
    put: true,          // 允许拖入
    pull: false         // 不允许拖出
  },
  onAdd: (evt) => handleComponentAdd(evt),
  onUpdate: (evt) => handleComponentMove(evt)
}
```

### 3.3 关键实现文件
1. `src/components/MaterialPanel.vue` - 物料面板
2. `src/components/Canvas.vue` - 画布（更新拖拽接收）
3. `src/composables/useDragDrop.ts` - 拖拽逻辑封装
4. `src/utils/dragHelper.ts` - 拖拽辅助函数

### 3.4 完成标准
- [ ] 能从物料区拖拽组件到画布
- [ ] 拖拽时显示预览效果
- [ ] 放置时根据鼠标位置确定组件坐标
- [ ] 支持网格吸附
- [ ] 拖拽过程流畅，无卡顿

---

## ⚙️ 第四阶段：属性配置

### 4.1 表单相关库

```bash
# JSON Schema表单库（推荐）
npm install @form-create/element-plus  # Element Plus生态的JSON Schema表单库

# 可选：表单设计器（如果需要可视化设计表单）
npm install @form-create/designer

# 代码编辑器（用于高级配置）
npm install monaco-editor       # VS Code同款编辑器
npm install @monaco-editor/loader

# 颜色选择器增强（Element Plus已包含，可选）
npm install @ctrl/tinycolor     # 颜色处理库
```

### 4.2 @form-create/element-plus 集成

#### 全局注册
```typescript
// main.ts
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import formCreate from '@form-create/element-plus'

const app = createApp(App)
app.use(ElementPlus)
app.use(formCreate)  // 注册 form-create
```

#### Schema到表单的自动映射
```typescript
// Schema 自动转换为 form-create 规则
function convertSchemaToFormRule(schema: any) {
  return Object.entries(schema.properties).map(([key, prop]: [string, any]) => ({
    type: getFormControlType(prop),  // 自动识别控件类型
    field: key,
    title: prop.title || key,
    value: prop.default,
    props: getFormControlProps(prop)
  }))
}

// 支持的控件类型映射
const CONTROL_TYPE_MAP = {
  'string': 'input',
  'string+enum': 'select',
  'string+color': 'ColorPicker',
  'string+textarea': 'input',
  'number': 'InputNumber',
  'boolean': 'switch',
  'array': 'checkbox',  // 或自定义数组编辑器
  'object': 'group'     // 或自定义对象编辑器
}
```

#### 关键组件
1. `src/components/PropertyPanel.vue` - 属性面板容器（使用form-create）
2. `src/utils/schemaConverter.ts` - Schema转换器
3. `src/components/CustomFormControls/` - 自定义表单控件（可选）

### 4.3 属性面板实现示例

#### PropertyPanel.vue 完整实现
```vue
<template>
  <div class="property-panel">
    <div class="panel-header">
      <h3>{{ selectedComponent?.name || '属性配置' }}</h3>
    </div>

    <div class="panel-content">
      <!-- 基础属性 -->
      <el-collapse v-model="activeNames" accordion>
        <el-collapse-item title="基础属性" name="basic">
          <BasicProperties
            v-if="selectedComponent"
            :component="selectedComponent"
            @update="updateBasicProps"
          />
        </el-collapse-item>

        <!-- 组件属性 -->
        <el-collapse-item title="组件属性" name="component">
          <form-create
            v-if="formRule.length > 0"
            v-model="formData"
            :rule="formRule"
            :option="formOption"
            @change="handleFormChange"
          />
          <div v-else class="no-props">该组件暂无可配置属性</div>
        </el-collapse-item>

        <!-- 样式属性 -->
        <el-collapse-item title="样式属性" name="style">
          <StyleProperties
            v-if="selectedComponent"
            :component="selectedComponent"
            @update="updateStyleProps"
          />
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { getMaterialDefinition } from '@/materials/registry'

const canvasStore = useCanvasStore()
const activeNames = ref(['basic', 'component'])

// 选中的组件
const selectedComponent = computed(() =>
  canvasStore.getSelectedComponent()
)

// 表单数据
const formData = ref({})

// 根据组件Schema生成表单规则
const formRule = computed(() => {
  if (!selectedComponent.value) return []

  const material = getMaterialDefinition(selectedComponent.value.type)
  if (!material?.schema) return []

  return convertSchemaToFormRule(material.schema)
})

// form-create 配置
const formOption = {
  submitBtn: false,
  resetBtn: false,
  form: {
    labelWidth: '80px',
    size: 'small'
  }
}

// 监听选中组件变化，更新表单数据
watch(selectedComponent, (newComponent) => {
  if (newComponent) {
    formData.value = { ...newComponent.props }
  }
}, { immediate: true })

// 表单变化处理
function handleFormChange(newFormData: any) {
  if (selectedComponent.value) {
    canvasStore.updateComponent(selectedComponent.value.id, {
      props: { ...newFormData }
    })
  }
}

// Schema转换为form-create规则
function convertSchemaToFormRule(schema: any) {
  if (!schema?.properties) return []

  return Object.entries(schema.properties).map(([key, prop]: [string, any]) => ({
    type: getFormControlType(prop),
    field: key,
    title: prop.title || key,
    value: prop.default,
    props: getFormControlProps(prop),
    validate: getValidateRules(prop)
  }))
}

function getFormControlType(prop: any) {
  if (prop.enum) return 'select'
  if (prop.format === 'color') return 'ColorPicker'
  if (prop.format === 'textarea') return 'input'
  if (prop.type === 'number') return 'InputNumber'
  if (prop.type === 'boolean') return 'switch'
  return 'input'
}

function getFormControlProps(prop: any) {
  const props: any = {}

  if (prop.format === 'textarea') {
    props.type = 'textarea'
    props.rows = 3
  }

  if (prop.type === 'number') {
    props.min = prop.minimum
    props.max = prop.maximum
    props.step = prop.step || 1
  }

  if (prop.enum) {
    props.options = prop.enum.map((value: any) => ({
      label: value,
      value: value
    }))
  }

  return props
}

function getValidateRules(prop: any) {
  const rules = []

  if (prop.required) {
    rules.push({ required: true, message: `请输入${prop.title}` })
  }

  if (prop.minimum !== undefined) {
    rules.push({ type: 'number', min: prop.minimum, message: `最小值为${prop.minimum}` })
  }

  if (prop.maximum !== undefined) {
    rules.push({ type: 'number', max: prop.maximum, message: `最大值为${prop.maximum}` })
  }

  return rules
}
</script>

<style lang="scss" scoped>
.property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;

    h3 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }

  .no-props {
    text-align: center;
    color: #909399;
    padding: 20px;
    font-size: 14px;
  }
}
</style>
```

### 4.4 完成标准
- [ ] 选中组件时显示对应的属性面板
- [ ] 支持所有基础属性类型的编辑（文本、数字、颜色、选择等）
- [ ] 修改属性能实时更新组件显示
- [ ] 支持属性分组和折叠
- [ ] 支持属性验证和错误提示
- [ ] 表单控件自动根据Schema生成

---

## 🔧 第五阶段：完善功能

### 5.1 历史管理和工具库

```bash
# 不可变数据库（用于历史记录）
npm install immer               # 不可变数据更新
npm install use-immer           # Immer的Vue组合式API

# 文件处理
npm install file-saver          # 文件下载
npm install jszip               # ZIP文件处理

# 图片处理
npm install html2canvas         # HTML转图片
npm install dom-to-image        # DOM转图片（备选）

# 快捷键
npm install hotkeys-js          # 快捷键库
npm install @vueuse/core        # 已安装，包含useKeyboard

# 工具库增强
npm install ramda               # 函数式编程工具库
npm install date-fns            # 日期处理库
```

### 5.2 功能模块

#### 历史管理模块
```typescript
// src/stores/history.ts
interface HistoryState {
  past: CanvasState[]
  present: CanvasState
  future: CanvasState[]
}

// 支持的操作
- undo() / redo()
- saveSnapshot()
- clearHistory()
```

#### 文件操作模块
```typescript
// src/utils/fileOperations.ts
- saveProject(data: ProjectData): void
- loadProject(file: File): Promise<ProjectData>
- exportAsImage(): Promise<Blob>
- exportAsJSON(): string
```

#### 快捷键模块
```typescript
// src/composables/useShortcuts.ts
const shortcuts = {
  'ctrl+z': () => historyStore.undo(),
  'ctrl+y': () => historyStore.redo(),
  'ctrl+c': () => canvasStore.copy(),
  'ctrl+v': () => canvasStore.paste(),
  'delete': () => canvasStore.deleteSelected()
}
```

### 5.3 完成标准
- [ ] 撤销/重做功能正常
- [ ] 复制/粘贴功能正常
- [ ] 删除组件功能正常
- [ ] 保存/加载设计稿功能正常
- [ ] 预览模式功能正常
- [ ] 导出图片功能正常
- [ ] 快捷键功能正常

---

## 📦 完整的package.json

```json
{
  "name": "visual-builder",
  "version": "1.0.0",
  "description": "可视化搭建平台",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "type-check": "vue-tsc --noEmit"
  },
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.4.0",
    "@element-plus/icons-vue": "^2.3.0",
    "nanoid": "^5.0.0",
    "lodash-es": "^4.17.21",
    "@vueuse/core": "^10.5.0",
    "@vueuse/gesture": "^2.0.0",
    "vuedraggable": "^4.1.0",
    "sortablejs": "^1.15.0",
    "@form-create/element-plus": "^3.1.0",
    "@form-create/designer": "^3.1.0",
    "monaco-editor": "^0.44.0",
    "@monaco-editor/loader": "^1.4.0",
    "@ctrl/tinycolor": "^3.6.0",
    "immer": "^10.0.0",
    "use-immer": "^0.9.0",
    "file-saver": "^2.0.5",
    "jszip": "^3.10.0",
    "html2canvas": "^1.4.0",
    "hotkeys-js": "^3.12.0",
    "ramda": "^0.29.0",
    "date-fns": "^2.30.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.5.0",
    "typescript": "^5.2.0",
    "vue-tsc": "^1.8.0",
    "vite": "^5.0.0",
    "sass": "^1.69.0",
    "@types/lodash-es": "^4.17.0",
    "@types/sortablejs": "^1.15.0",
    "@types/file-saver": "^2.0.0",
    "@types/ramda": "^0.29.0"
  }
}
```

---

## 🎯 开发建议和最佳实践

### 技术选择原则
1. **优先选择Vue生态的库** - 更好的集成度
2. **选择活跃维护的库** - 避免过时的依赖
3. **考虑包大小** - 影响加载性能
4. **TypeScript友好** - 提升开发体验
5. **技术栈统一** - 如选择@form-create/element-plus而非其他UI库的表单方案

### 性能优化建议
1. **组件懒加载** - 使用动态import
2. **虚拟滚动** - 大量组件时使用
3. **防抖节流** - 频繁操作时使用
4. **Web Worker** - 复杂计算放到Worker中

### 调试技巧
1. **Vue DevTools** - 调试状态和组件
2. **浏览器控制台** - 直接操作store测试
3. **性能面板** - 分析渲染性能
4. **Network面板** - 检查资源加载

### 常见问题和解决方案
1. **坐标计算错误** - 注意画布变换矩阵
2. **事件冲突** - 正确处理事件冒泡和捕获
3. **内存泄漏** - 及时清理事件监听器
4. **类型错误** - 完善TypeScript类型定义

---

## 🏆 项目成功标准

### 第一阶段成功标准
```javascript
// 能够运行这样的代码并看到正确效果
const canvasStore = useCanvasStore()
canvasStore.addComponent({
  type: 'Text',
  name: '测试',
  x: 100, y: 100,
  width: 200, height: 50,
  props: { text: 'Hello World', color: '#ff0000' }
})
// 页面应该显示红色的"Hello World"文本
```

### 最终成功标准
- ✅ 能拖拽组件到画布
- ✅ 能选中和编辑组件属性
- ✅ 能保存和加载设计稿
- ✅ 整个过程流畅无卡顿
- ✅ 支持撤销重做
- ✅ 支持预览和导出

---

## 📚 学习资源

### 官方文档
- [Vue 3 官方文档](https://cn.vuejs.org/)
- [Pinia 官方文档](https://pinia.vuejs.org/zh/)
- [Element Plus 官方文档](https://element-plus.org/zh-CN/)
- [VueUse 官方文档](https://vueuse.org/)

### 参考项目
- [GoView](https://github.com/dromara/go-view) - 开源数据可视化平台
- [DataV](https://github.com/DataV-Team/DataV) - Vue数据可视化组件库
- [Luckysheet](https://github.com/mengshukeji/Luckysheet) - 在线表格

### 技术博客
- [Vue 3 Composition API 最佳实践](https://vuejs.org/guide/extras/composition-api-faq.html)
- [TypeScript 在 Vue 项目中的应用](https://vuejs.org/guide/typescript/overview.html)
- [前端拖拽功能实现原理](https://developer.mozilla.org/zh-CN/docs/Web/API/HTML_Drag_and_Drop_API)

---

## 🚀 快速开始

### 立即开始第一阶段
```bash
# 1. 创建项目
npm create vue@latest visual-builder
cd visual-builder

# 2. 安装第一阶段依赖
npm install pinia element-plus @element-plus/icons-vue nanoid lodash-es
npm install -D sass @types/lodash-es

# 3. 第四阶段时安装表单库
npm install @form-create/element-plus

# 4. 开始编码
# 按照文档第1.4节的步骤，先实现ComponentRenderer.vue
```

### 开发检查清单
- [ ] 第一阶段：JSON渲染组件 ✅
- [ ] 第二阶段：基础画布 ⏳
- [ ] 第三阶段：拖拽系统 ⏳
- [ ] 第四阶段：属性配置 ⏳
- [ ] 第五阶段：完善功能 ⏳

---

**记住核心原则：先实现核心功能，再完善用户体验！JSON渲染组件是整个系统的心脏，一定要先做好这个！**
