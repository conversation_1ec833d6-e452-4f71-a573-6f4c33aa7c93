//组件类型定义
/**
 * 组件类型
 */
export interface ComponentInstance {
  id: string; // 唯一标识，使用nanoid生成
  type: string; // 组件类型，如 'Text', 'BarChart', 'Image'
  name: string; // 组件名称，用于图层面板显示
  x: number; // X坐标（像素）
  y: number; // Y坐标（像素）
  width: number; // 宽度（像素   ）
  height: number; // 高度（像素）
  zIndex: number; // 层级，数值越大越在上层
  rotation: number; // 旋转角度（度）
  opacity: number; // 透明度 0-1
  visible: boolean; // 是否可见
  locked: boolean; // 是否锁定（锁定后不可编辑）
  props: Record<string, any>; // 组件特有属性
  style: Record<string, any>; // CSS样式
  //   events?: ComponentEvent[]; // 事件配置（第四阶段）
  //   animations?: Animation[]; // 动画配置（第五阶段）
}
/**
 * 物料组件定义
 */
export interface MaterialDefinition {
  type: string; // 组件类型，全局唯一
  name: string; // 显示名称
  category: string; // 分类：'basic', 'chart', 'media', 'layout'
  icon: string; // 图标名称（Element Plus图标）
  thumbnail?: string; // 缩略图URL
  component: any; // Vue组件（动态导入）
  schema: ComponentSchema; // 属性配置Schema
  defaultProps: Record<string, any>; // 默认属性
  defaultStyle: Record<string, any>; // 默认样式
  defaultSize: {
    // 默认尺寸
    width: number;
    height: number;
  };
}
/**
 * 组件属性Schema（JSON Schema格式）
 */
export interface ComponentSchema {
  type: "object";
  properties: Record<string, PropertySchema>;
  required?: string[];
}
/**
 * 属性Schema
 */
export interface PropertySchema {
  type: "string" | "number" | "boolean" | "array" | "object";
  title: string; // 属性显示名称
  description?: string; // 属性描述
  default?: any; // 默认值
  enum?: any[]; // 枚举值（用于下拉选择）
  minimum?: number; // 数字最小值
  maximum?: number; // 数字最大值
  step?: number; // 数字步长
  format?: "color" | "textarea" | "url" | "email" | "date"; // 特殊格式
  items?: PropertySchema; // 数组项Schema
}
