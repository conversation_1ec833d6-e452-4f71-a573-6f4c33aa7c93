import request from '@/utils/request'

// 查询办案中心设备信息列表
export function listDevice(query) {
  return request({
    url: '/basic/device/list',
    method: 'get',
    params: query
  })
}

// 查询办案中心设备信息详细
export function getDevice(id) {
  return request({
    url: '/basic/device/' + id,
    method: 'get'
  })
}

// 新增办案中心设备信息
export function addDevice(data) {
  return request({
    url: '/basic/device',
    method: 'post',
    data: data
  })
}

// 修改办案中心设备信息
export function updateDevice(data) {
  return request({
    url: '/basic/device',
    method: 'put',
    data: data
  })
}

// 删除办案中心设备信息
export function delDevice(id) {
  return request({
    url: '/basic/device/' + id,
    method: 'delete'
  })
}
