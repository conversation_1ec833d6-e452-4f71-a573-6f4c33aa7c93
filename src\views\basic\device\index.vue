<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="设备ip" prop="ip">
        <el-input v-model="queryParams.ip" placeholder="请输入设备ip地址" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['basic:device:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['basic:device:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备名称" align="center" prop="name" />
      <el-table-column label="设备ip地址" align="center" prop="ip" />
      <el-table-column label="办案中心" align="center" prop="placeName" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 0" size="small" type="danger">离线</el-tag>
          <el-tag v-else size="small" type="success">在线</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="最后在线时间" align="center" prop="lastOnlineTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastOnlineTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="高度" align="center" prop="height" />
      <el-table-column label="宽度" align="center" prop="weight" />
      <el-table-column label="设备类型" align="center" prop="type">
        <template #default="scope">
          <el-tag v-if="scope.row.type === 0">大屏</el-tag>
          <el-tag v-if="scope.row.type === 1">报警终端</el-tag>
          <el-tag v-if="scope.row.type === 2">门头屏</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basic:device:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basic:device:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改办案中心设备信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="deviceRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备ip" prop="ip">
          <el-input v-model="form.ip" readonly />
        </el-form-item>
        <el-form-item label="设备类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择设备类型">
            <el-option v-for="item in deviceTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="办案中心" prop="placeId">
          <el-select v-model="form.placeId" placeholder="请选择" clearable>
            <el-option v-for="item in centerPlaceList" :key="item.fid" :label="item.fplacename" :value="item.fid"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="高度" prop="height">
          <el-input v-model="form.height" placeholder="请输入高度" />
        </el-form-item>
        <el-form-item label="宽度" prop="weight">
          <el-input v-model="form.weight" placeholder="请输入宽度" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Device">
import { listDevice, getDevice, delDevice, addDevice, updateDevice } from "@/api/basic/device"
import { getCenterPlaceList } from "@/api/visgkpt/platFormTable"

const { proxy } = getCurrentInstance()

const deviceList = ref([])
const centerPlaceList = ref([])
const deviceTypeList = ref([
  {
    label: "大屏",
    value: 0
  },
  {
    label: "报警终端",
    value: 1
  },
  {
    label: "门头屏",
    value: 2
  }
])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    ip: null,
    placeId: null,
    status: null,
    lastOnLineTime: null,
    height: null,
    weight: null,
    type: null,
    isDelete: null,
  },
  rules: {
    name: [
      { required: true, message: "请输入名称", trigger: "blur" }
    ],
    placeId: [
      { required: true, message: "请选择办案中心", trigger: "chang" }
    ],
    type: [
      { required: true, message: "请选择设备状态", trigger: "chang" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

function getCenterPlace() {
  getCenterPlaceList().then(res => {
    centerPlaceList.value = res.rows;
  })
}
/** 查询办案中心设备信息列表 */
function getList() {
  loading.value = true
  listDevice(queryParams.value).then(response => {
    deviceList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    ip: null,
    placeId: null,
    status: null,
    lastOnLineTime: null,
    height: null,
    weight: null,
    type: null,
    createBy: null,
    createTime: null,
    isDelete: null,
    updateBy: null,
    updateTime: null,
    remark: null
  }
  proxy.resetForm("deviceRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  getCenterPlace()
  open.value = true
  title.value = "添加办案中心设备信息"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  getCenterPlace()
  const _id = row.id || ids.value
  getDevice(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改设备信息"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["deviceRef"].validate(valid => {
    if (valid) {
      var centerPlace = centerPlaceList.value.filter(item => item.id == form.value.centerPlaceId)[0]
      form.value.placeCode = centerPlace.fplacecode
      form.value.placeName = centerPlace.fplacename
      if (form.value.id != null) {
        updateDevice(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addDevice(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除办案中心设备信息编号为"' + _ids + '"的数据项？').then(function () {
    return delDevice(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('basic/device/export', {
    ...queryParams.value
  }, `device_${new Date().getTime()}.xlsx`)
}

getList()
</script>
