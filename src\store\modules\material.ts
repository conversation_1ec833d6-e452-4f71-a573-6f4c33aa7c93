import { defineStore } from "pinia";
import { materialRegistry } from "../../materials/registry";
export const materialStore = defineStore("material", {
  state: () => ({
    registry: materialRegistry,
    /** 选中的物料分类 */
    selectedCategory: "all",
    /** 搜索关键词 */
    searchKey: "" as string,
  }),
  getters: {
    /**物料列表 */
    materials: (state) => state.registry.getAll(),
    /**按类删选物料 */
    filteredMaterials: (state) => {
      let materials = state.registry.getAll();
      if (state.selectedCategory !== "all") {
        materials = materials.filter(
          (material) => material.category === state.selectedCategory
        );
      }
      if (state.searchKey) {
        materials = materials.filter(
          (m) =>
            m.name.includes(state.searchKey) || m.type.includes(state.searchKey)
        );
      }
      return materials;
    },
  },
  actions: {},
});
