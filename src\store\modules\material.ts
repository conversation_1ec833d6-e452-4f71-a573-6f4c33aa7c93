import { defineStore } from "pinia";
import { materialRegistry } from "../../materials/registry";
export const materialStore = defineStore("material", {
  state: () => ({
    registry: materialRegistry,
    /** 选中的物料分类 */
    selectedCategory: "all",
    /** 搜索关键词 */
    searchKey: "" as string,
  }),
  getters: {
    /**物料列表 */
    materials: (state) => state.registry.getAll(),
    /**按类筛选物料 */
    filteredMaterials: (state) => {
      let materials = state.registry.getAll();
      if (state.selectedCategory !== "all") {
        materials = materials.filter(
          (material) => material.category === state.selectedCategory
        );
      }
      if (state.searchKey) {
        materials = materials.filter(
          (m) =>
            m.name.includes(state.searchKey) || m.type.includes(state.searchKey)
        );
      }
      return materials;
    },
  },
  actions: {
    // 设置分类筛选
    setCategory(category: string) {
      this.selectedCategory = category;
    },

    // 设置搜索关键词
    setSearchKeyword(keyword: string) {
      this.searchKeyword = keyword;
    },

    // 获取组件定义（代理到 registry）
    getComponent(type: string) {
      return this.registry.get(type)?.component;
    },

    // 获取组件 Schema（代理到 registry）
    getSchema(type: string) {
      return this.registry.get(type)?.schema;
    },

    // 获取默认属性
    getDefaultProps(type: string) {
      return this.registry.get(type)?.defaultProps || {};
    },
  },
});
